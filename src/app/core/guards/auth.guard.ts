import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';

import { AuthenticationService } from '../services/auth.service';
import { AuthfakeauthenticationService } from '../services/authfake.service';

@Injectable({ providedIn: 'root' })
export class AuthGuard implements CanActivate {
  constructor(
    private router: Router,
    private authenticationService: AuthenticationService,
    private authFackservice: AuthfakeauthenticationService
  ) { }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {

    const currentUser = this.authFackservice.currentUserValue;
    if (currentUser) {
      // logged in so return true
      if (state.url.includes('/account/login')) {
        this.router.navigate(['/dashboard']);
        return false;
      }
      return true;
    }
    if (state.url === '/account/login') {
      return true;
    }
    // if (state.url.startsWith('/inspection-detail/')) {
    //   return  true;
    //   // return this.router.navigate(['/']);
    // }
    // if (state.url.startsWith('/evaluation-results')) {
    //   return true;
    // }

    if (state.url.startsWith('/tuketim-tablosu')) {
      return true;
    }
    // not logged in so redirect to login page with the return url
    this.router.navigate(['/account/login']);
    return false;
  }
}
