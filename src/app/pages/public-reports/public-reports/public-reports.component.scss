:root {
  --black: 	#21252a;
  --grey-1: #343A40;
  --grey-2: #495057;
  --grey-3: #868E96;
  --grey-4: #ADB5BD;
  --grey-5: #CED4DA;
  --grey-6: #DEE2E6;
  --grey-7: #E9ECEF;
  --grey-8: #F1F3F5;
  --grey-9: #F8F9FA;
  --green: green;
  --green-1: rgb(4, 155, 4);
  --green-2: rgb(0, 189, 0);
  --green-4: #00b894;
  --green-5: #55efc4;


  --trans-black: rgba(33, 37, 42, .9);

  --red: #e10600;

  --gold: #ffda65;
  --gold-dark: #a3862c;
  --bronze: #c99355;
  --bronze-dark: #80582c;
}

@mixin fade {
  opacity: 0;
  position: relative;
  left: 100px;
  animation: fade 500ms ease 150ms forwards;
}

html {
  box-sizing: border-box;
}

*, *:before, *:after {
  box-sizing: inherit;
}

html, body {
  width: 100%;
  height: 100%;
}
body {
  font-family: 'Inter UI', system-ui;
  color: var(--black);
  background: var(--black);
}

.list {
  width: 100%;
  margin: 1rem auto 1rem;
  border-radius: .4rem;
  @media screen and (max-width: 800px) {
    margin: 0 auto;
  }
  box-shadow: 0px 12px 25px rgba(black, .1), 0px 5px 12px rgba(black, .07);
  &__table {
    width: 100%;
    border-spacing: 0;
    color: var(--grey-3);
  }
  &__header {
    padding: 3rem 2rem;
    background: white;
    border-top-left-radius: .4rem;
    border-top-right-radius: .4rem;
    h1, h5 {
      margin: 0;
      padding: 0;
    }
    h5 {
      margin-bottom: .5rem;
      text-transform: uppercase;
      color: var(--green);
    }
  }
  &__value {
    display: block;
    font-size: 22px;
  }
  &__label {
    font-size: 11px;
    opacity: .6;
  }
  &__row {
    background: var(--grey-7);
    cursor: pointer;
    transition: all 300ms ease;
    &:hover, &:focus {
      transform: scale(1.05);
      box-shadow: 0px 15px 28px rgba(black, .1), 0px 5px 12px rgba(black, .08);
      transition: all 300ms ease;
    }
    &:not(:last-of-type) {
      .list__cell {
        box-shadow: 0px 2px 0px rgba(black, .08);
      }
    }
  }
  &__cell {
    padding:2rem;
    &:first-of-type {
      text-align: center;
      padding: 2rem .2rem;
      background: var(--grey-5);
    }
  }
}
.uni{
  font-size: 16px;
}
.overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--trans-black);
  display: none;
  cursor: pointer;
  transition: all 300ms ease;
  &.is-open {
    display: block;
  }
}

.sidebar {
  $size: 500px;
  position: fixed;
  background: white;
  width: 100%;
  max-width: $size;
  top: 0;
  bottom: 0;
  box-shadow: none;
  right: -$size;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  /*@media screen and (max-width: 650px) {
    flex-direction: column-reverse;
    justify-content: space-between;
  }*/
  transition: all 300ms ease;
  &.is-open {
    right: 0;
    transition: all 300ms ease;
    box-shadow: 0px 0px 100px var(--black);
  }
  &__header {
    display: flex;
    justify-content: space-between;
    background: var(--grey-9);
    align-items: center;
  }
  &__header, &__body {
    padding: 2rem;
  }
  &__title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--grey-4);
  }
}

.button {
  font-family: inherit;
  border: 0;
  background: transparent;
  cursor: pointer;
  &:focus, &:active {
    outline: 0;
  }
  &--close {
    padding: 0;
    margin: 0;
    height: auto;
    line-height: 1;
    color: var(--grey-5);
    &:hover {
      color: var(--grey--4);
    }
  }
}

.driver {
  display: flex;
  align-items: flex-start;
  @include fade;
  &__image {
    $size: 100px;
    width: $size;
    height: $size;
    border-radius: 50%;
    background-size: 220px;
    background-repeat: no-repeat;
    background-position: top center;
    border: 3px solid white;
    box-shadow: 0px 5px 12px rgba(black, .12);
    margin-right: 1.5rem;
  }
  &__content {
    width: auto;
  }
  &__title {
    font-weight: 700;
    font-size: 1.6rem;
    margin: .5rem 0
  }
  &__table {
    width: 100%;
    color: var(--grey-2);
    small {
      color: var(--grey-4);
    }
    td {
      padding: .3rem .6rem .3rem 0;
      height: 2rem;
      img {
        position:relative;
        top: 5px;
        margin-right: 6px;
      }
    }
  }
}

@keyframes fade {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
    left: 0;
  }
}
