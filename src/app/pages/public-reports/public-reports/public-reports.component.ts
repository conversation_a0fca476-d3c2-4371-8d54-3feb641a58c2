import { Component, OnInit } from '@angular/core';
import { RaceService } from 'src/app/core/services/race.service';
import { Router, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-public-reports',
  templateUrl: './public-reports.component.html',
  styleUrls: ['./public-reports.component.scss']
})
export class PublicReportsComponent implements OnInit {

  breadCrumbItems: Array<{}>;
  leaderBoard;
  sessionId;
  raceList: any[] = [];
  selectedRaceId: string;
  constructor(private raceService: RaceService, private route: ActivatedRoute, private router: Router) { }

  ngOnInit() {
    this.breadCrumbItems = [{ label: '' }];
    this.route.queryParams.subscribe(params => {
      this.sessionId = params.id;
      this.selectedRaceId = this.sessionId;
      this.getSessionDetail();
    });
    this.getRaceList();
  }

  getRaceList() {
    this.raceService.raceList(true).subscribe((res: any) => {
      if (res && res.data) {
        this.raceList = res.data;
      }
      console.log(this.raceList)
      this.router.navigate([], {
      queryParams: { id : this.raceList[0].id },
      queryParamsHandling: 'merge',
    });
    });
  }

  onRaceSelect(event: any) {
    const selectedId = event.target.value;
    this.router.navigate([], {
      queryParams: { id: selectedId },
      queryParamsHandling: 'merge',
    });
    
  }

  getSessionDetail() {
    this.raceService.getSession(this.sessionId, true).subscribe(data => {
      let teams = data['data'].race_teams;
      console.log(teams)
       // team_id'ye göre küçükten büyüğe sırala
      teams = teams.sort((a: { team_id: number; }, b: { team_id: number; }) => a.team_id - b.team_id);
      this.leaderBoard = teams;

    });
  }

}
